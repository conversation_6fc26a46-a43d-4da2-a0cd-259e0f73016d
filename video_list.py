# -*- coding: utf-8 -*-
"""
视频列表组件
"""
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QLabel, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from utils import get_video_files, format_time, get_video_duration, ThumbnailGenerator, get_thumbnail_path
from config import settings_manager


class VideoListWidget(QWidget):
    """视频列表组件"""

    # 信号定义
    video_selected = pyqtSignal(str)  # 视频被选中
    video_changed = pyqtSignal(int, int)  # 视频切换 (当前索引, 总数)

    def __init__(self):
        super().__init__()
        self.video_files = []
        self.current_index = -1
        self.folder_path = None

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 标题
        title_label = QLabel("视频列表")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 统计信息
        self.info_label = QLabel("共 0 个视频")
        self.info_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.info_label)

        # 视频列表
        self.video_list = QListWidget()
        self.video_list.setMinimumWidth(250)
        self.video_list.setAlternatingRowColors(True)
        layout.addWidget(self.video_list)





    def setup_connections(self):
        """设置信号连接"""
        self.video_list.itemClicked.connect(self.on_video_item_clicked)
        self.video_list.currentRowChanged.connect(self.on_current_row_changed)

    def load_folder(self, folder_path):
        """加载文件夹中的视频"""
        self.folder_path = folder_path
        self.video_files = get_video_files(folder_path)
        self.current_index = -1

        # 清空列表
        self.video_list.clear()

        # 加载视频到列表
        for i, video_path in enumerate(self.video_files):
            item = QListWidgetItem()
            widget = VideoItemWidget(video_path, i)

            # 检查是否已提取
            if settings_manager.is_video_extracted(folder_path, video_path):
                widget.set_extracted(True)

            item.setSizeHint(widget.sizeHint())
            self.video_list.addItem(item)
            self.video_list.setItemWidget(item, widget)

        # 更新信息
        self.update_info()
        self.update_navigation_buttons()

        # 恢复上次的视频位置
        progress = settings_manager.get_video_progress(folder_path)
        last_index = progress.get('current_video_index', 0)
        if 0 <= last_index < len(self.video_files):
            self.select_video(last_index)
        elif self.video_files:
            self.select_video(0)



    def update_info(self):
        """更新统计信息"""
        total = len(self.video_files)
        extracted = sum(1 for video_path in self.video_files
                       if settings_manager.is_video_extracted(self.folder_path, video_path))

        self.info_label.setText(f"共 {total} 个视频 (已提取: {extracted})")

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        # 导航按钮已移到视频播放器中
        pass

    def select_video(self, index):
        """选择视频"""
        if 0 <= index < len(self.video_files):
            self.current_index = index
            self.video_list.setCurrentRow(index)

            video_path = self.video_files[index]
            self.video_selected.emit(video_path)
            self.video_changed.emit(index + 1, len(self.video_files))

            # 保存当前视频索引
            if self.folder_path:
                progress = settings_manager.get_video_progress(self.folder_path)
                progress['current_video_index'] = index
                settings_manager.set_video_progress(self.folder_path, progress)

            self.update_navigation_buttons()

    def select_previous_video(self):
        """选择上一个视频"""
        if self.current_index > 0:
            self.select_video(self.current_index - 1)

    def select_next_video(self):
        """选择下一个视频"""
        if self.current_index < len(self.video_files) - 1:
            self.select_video(self.current_index + 1)

    def on_video_item_clicked(self, item):
        """视频项被点击"""
        row = self.video_list.row(item)
        self.select_video(row)

    def on_current_row_changed(self, row):
        """当前行改变"""
        if row >= 0:
            self.select_video(row)

    def mark_current_video_extracted(self):
        """标记当前视频已提取"""
        if self.current_index >= 0:
            # 更新列表项显示
            item = self.video_list.item(self.current_index)
            if item:
                widget = self.video_list.itemWidget(item)
                if isinstance(widget, VideoItemWidget):
                    widget.set_extracted(True)

            # 更新统计信息
            self.update_info()






class VideoItemWidget(QWidget):
    """视频列表项组件"""

    def __init__(self, video_path, index):
        super().__init__()
        self.video_path = video_path
        self.index = index
        self.is_extracted = False
        self.thumbnail_generator = None

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(10)

        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(80, 45)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #2C2C2C;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                font-size: 10px;
            }
        """)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setText("加载中...")

        # 异步加载缩略图
        self.load_thumbnail()

        # 信息区域
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)

        # 文件名
        filename = os.path.basename(self.video_path)
        self.name_label = QLabel(filename)
        self.name_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.name_label.setWordWrap(True)

        # 状态和时长
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 0, 0, 0)

        self.status_label = QLabel("未提取")
        self.status_label.setFont(QFont("Arial", 9))
        self.status_label.setStyleSheet("color: #666;")

        # 获取视频时长
        duration = get_video_duration(self.video_path)
        duration_text = format_time(duration) if duration > 0 else "未知"
        self.duration_label = QLabel(duration_text)
        self.duration_label.setFont(QFont("Arial", 9))
        self.duration_label.setStyleSheet("color: #666;")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.duration_label)

        info_layout.addWidget(self.name_label)
        info_layout.addLayout(status_layout)

        layout.addWidget(self.thumbnail_label)
        layout.addLayout(info_layout)

        self.setMinimumHeight(65)
        self.setMaximumHeight(65)

    def load_thumbnail(self):
        """异步加载缩略图"""
        try:
            # 先检查缩略图是否已存在
            thumbnail_path = get_thumbnail_path(self.video_path, 80, 45)
            if thumbnail_path and os.path.exists(thumbnail_path):
                # 直接加载已存在的缩略图
                self.set_thumbnail(thumbnail_path)
            else:
                # 异步生成缩略图
                self.thumbnail_generator = ThumbnailGenerator(self.video_path, 80, 45)
                self.thumbnail_generator.thumbnail_ready.connect(self.on_thumbnail_ready)
                self.thumbnail_generator.thumbnail_failed.connect(self.on_thumbnail_failed)
                self.thumbnail_generator.start()
        except Exception as e:
            print(f"加载缩略图失败: {e}")
            self.thumbnail_label.setText("加载失败")

    def on_thumbnail_ready(self, video_path, thumbnail_path):
        """缩略图生成完成"""
        if video_path == self.video_path:
            self.set_thumbnail(thumbnail_path)

    def on_thumbnail_failed(self, video_path):
        """缩略图生成失败"""
        if video_path == self.video_path:
            self.thumbnail_label.setText("无缩略图")

    def set_thumbnail(self, thumbnail_path):
        """设置缩略图"""
        try:
            pixmap = QPixmap(thumbnail_path)
            if not pixmap.isNull():
                # 缩放图片以适应标签大小
                scaled_pixmap = pixmap.scaled(
                    80, 45, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                self.thumbnail_label.setPixmap(scaled_pixmap)
                self.thumbnail_label.setText("")
            else:
                self.thumbnail_label.setText("无法加载")
        except Exception as e:
            print(f"设置缩略图失败: {e}")
            self.thumbnail_label.setText("加载失败")

    def set_extracted(self, extracted):
        """设置提取状态"""
        self.is_extracted = extracted
        if extracted:
            self.status_label.setText("已提取")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            # 设置整个项目为淡色
            self.setStyleSheet("""
                VideoItemWidget {
                    background-color: #F0F8F0;
                    opacity: 0.7;
                }
                VideoItemWidget:hover {
                    background-color: #E8F5E8;
                    opacity: 0.9;
                }
            """)
            # 设置缩略图为淡色
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    background-color: #2C2C2C;
                    border: 1px solid #4CAF50;
                    border-radius: 4px;
                    color: white;
                    font-size: 10px;
                    opacity: 0.7;
                }
            """)
        else:
            self.status_label.setText("未提取")
            self.status_label.setStyleSheet("color: #666;")
            self.setStyleSheet("")
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    background-color: #2C2C2C;
                    border: 1px solid #555;
                    border-radius: 4px;
                    color: white;
                    font-size: 10px;
                }
            """)



