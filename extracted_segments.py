# -*- coding: utf-8 -*-
"""
已提取片段列表组件
"""
import os
import subprocess
from PyQt5.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QScrollArea, QLabel,
    QMenu, QAction, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from utils import format_time, get_video_duration, get_or_create_thumbnail
from config import settings_manager


class ExtractedSegmentsList(QWidget):
    """已提取片段列表组件"""

    # 信号定义
    segment_selected = pyqtSignal(str)  # 片段被选中

    def __init__(self):
        super().__init__()
        self.segments = []  # 存储片段信息
        self.current_folder = None

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 标题
        title_label = QLabel("已提取片段")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 滚动内容容器
        self.scroll_widget = QWidget()
        self.scroll_layout = QHBoxLayout(self.scroll_widget)
        self.scroll_layout.setContentsMargins(5, 5, 5, 5)
        self.scroll_layout.setSpacing(10)

        self.scroll_area.setWidget(self.scroll_widget)
        layout.addWidget(self.scroll_area)

        # 如果没有片段，显示提示
        self.empty_label = QLabel("暂无已提取的片段")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("color: #999; font-size: 14px;")
        layout.addWidget(self.empty_label)

        self.update_display()

    def load_folder(self, folder_path):
        """加载文件夹的已提取片段"""
        self.current_folder = folder_path
        self.refresh_segments()

    def refresh_segments(self):
        """刷新片段列表"""
        self.segments = []

        if not self.current_folder:
            self.update_display()
            return

        # 获取输出目录
        output_folder = settings_manager.get('output_folder')
        if not output_folder or not os.path.exists(output_folder):
            self.update_display()
            return

        # 扫描输出目录中的视频文件
        try:
            for file in os.listdir(output_folder):
                if file.lower().endswith(('.mp4', '.avi', '.mkv', '.mov')):
                    file_path = os.path.join(output_folder, file)

                    # 解析文件名获取信息
                    segment_info = self.parse_segment_filename(file, file_path)
                    if segment_info:
                        self.segments.append(segment_info)
        except Exception as e:
            print(f"扫描已提取片段失败: {e}")

        # 按创建时间排序
        self.segments.sort(key=lambda x: x.get('created_time', 0), reverse=True)
        self.update_display()

    def parse_segment_filename(self, filename, file_path):
        """解析片段文件名获取信息"""
        try:
            # 获取文件信息
            stat = os.stat(file_path)
            duration = get_video_duration(file_path)

            # 尝试从文件名解析原始视频名和片段信息
            base_name = os.path.splitext(filename)[0]

            # 查找 _segment_ 模式
            if '_segment_' in base_name:
                parts = base_name.split('_segment_')
                original_name = parts[0]
                segment_num = parts[1] if len(parts) > 1 else "1"
            else:
                original_name = base_name
                segment_num = "1"

            return {
                'filename': filename,
                'file_path': file_path,
                'original_name': original_name,
                'segment_num': segment_num,
                'duration': duration,
                'file_size': stat.st_size,
                'created_time': stat.st_mtime
            }
        except Exception as e:
            print(f"解析片段文件失败 {filename}: {e}")
            return None

    def update_display(self):
        """更新显示"""
        # 清空现有内容
        for i in reversed(range(self.scroll_layout.count())):
            child = self.scroll_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.segments:
            self.empty_label.setVisible(True)
            self.scroll_area.setVisible(False)
        else:
            self.empty_label.setVisible(False)
            self.scroll_area.setVisible(True)

            # 添加片段项
            for segment in self.segments:
                segment_item = ExtractedSegmentItem(segment)
                segment_item.delete_requested.connect(self.delete_segment)
                segment_item.open_location_requested.connect(self.open_file_location)
                segment_item.play_requested.connect(self.play_segment)
                self.scroll_layout.addWidget(segment_item)

            # 添加弹性空间
            self.scroll_layout.addStretch()

    def delete_segment(self, file_path):
        """删除片段"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除片段文件吗？\n{os.path.basename(file_path)}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    QMessageBox.information(self, "删除成功", "片段文件已删除")
                else:
                    QMessageBox.warning(self, "文件不存在", "文件已不存在，将从列表中移除")

                # 无论文件是否存在，都刷新列表
                self.refresh_segments()
            except PermissionError:
                QMessageBox.critical(self, "删除失败", "没有权限删除该文件，请检查文件是否被其他程序占用")
            except Exception as e:
                QMessageBox.critical(self, "删除失败", f"删除文件时出错:\n{str(e)}")
                # 即使删除失败，也刷新列表以更新状态
                self.refresh_segments()

    def open_file_location(self, file_path):
        """在资源管理器中打开文件位置"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['explorer', '/select,', file_path])
            elif os.name == 'posix':  # Linux/Mac
                subprocess.run(['xdg-open', os.path.dirname(file_path)])
        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"无法打开文件位置:\n{str(e)}")

    def play_segment(self, file_path):
        """播放片段"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # Linux/Mac
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            QMessageBox.warning(self, "播放失败", f"无法播放文件:\n{str(e)}")


class ExtractedSegmentItem(QWidget):
    """已提取片段项组件"""

    # 信号定义
    delete_requested = pyqtSignal(str)  # 请求删除
    open_location_requested = pyqtSignal(str)  # 请求打开位置
    play_requested = pyqtSignal(str)  # 请求播放

    def __init__(self, segment_info):
        super().__init__()
        self.segment_info = segment_info
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)

        # 缩略图区域
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(120, 68)
        self.thumbnail_label.setStyleSheet(
            "background-color: #2C2C2C; "
            "border: 1px solid #555; "
            "border-radius: 4px; "
            "color: white; "
            "font-size: 12px;"
        )
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setText("加载中...")

        # 加载缩略图
        self.load_thumbnail()

        # 文件名
        filename = self.segment_info['filename']
        name_label = QLabel(filename)
        name_label.setFont(QFont("Arial", 9, QFont.Bold))
        name_label.setWordWrap(True)
        name_label.setMaximumWidth(120)
        name_label.setAlignment(Qt.AlignCenter)

        # 时长信息
        duration = self.segment_info.get('duration', 0)
        duration_text = format_time(duration) if duration > 0 else "未知"
        duration_label = QLabel(f"时长: {duration_text}")
        duration_label.setFont(QFont("Arial", 8))
        duration_label.setAlignment(Qt.AlignCenter)
        duration_label.setStyleSheet("color: #666;")

        # 文件大小
        file_size = self.segment_info.get('file_size', 0)
        size_text = self.format_file_size(file_size)
        size_label = QLabel(f"大小: {size_text}")
        size_label.setFont(QFont("Arial", 8))
        size_label.setAlignment(Qt.AlignCenter)
        size_label.setStyleSheet("color: #666;")

        layout.addWidget(self.thumbnail_label)
        layout.addWidget(name_label)
        layout.addWidget(duration_label)
        layout.addWidget(size_label)

        self.setFixedWidth(130)
        self.setToolTip(f"右键查看更多选项\n文件: {filename}")

        # 设置样式
        self.setStyleSheet("""
            ExtractedSegmentItem {
                background-color: #F5F5F5;
                border: 1px solid #DDD;
                border-radius: 6px;
            }
            ExtractedSegmentItem:hover {
                background-color: #E8F4FD;
                border-color: #0078D4;
            }
        """)

    def load_thumbnail(self):
        """加载缩略图"""
        try:
            thumbnail_path = get_or_create_thumbnail(self.segment_info['file_path'], 120, 68)
            if thumbnail_path and os.path.exists(thumbnail_path):
                pixmap = QPixmap(thumbnail_path)
                if not pixmap.isNull():
                    # 缩放图片以适应标签大小
                    scaled_pixmap = pixmap.scaled(
                        120, 68, Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    self.thumbnail_label.setPixmap(scaled_pixmap)
                    self.thumbnail_label.setText("")
                else:
                    self.thumbnail_label.setText("无法加载")
            else:
                self.thumbnail_label.setText("无缩略图")
        except Exception as e:
            print(f"加载片段缩略图失败: {e}")
            self.thumbnail_label.setText("加载失败")

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            # 双击播放
            pass
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.LeftButton:
            self.play_requested.emit(self.segment_info['file_path'])
        super().mouseDoubleClickEvent(event)

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)

        # 播放
        play_action = QAction("播放", self)
        play_action.triggered.connect(
            lambda: self.play_requested.emit(self.segment_info['file_path'])
        )
        menu.addAction(play_action)

        menu.addSeparator()

        # 在资源管理器中打开
        open_location_action = QAction("在资源管理器中显示", self)
        open_location_action.triggered.connect(
            lambda: self.open_location_requested.emit(self.segment_info['file_path'])
        )
        menu.addAction(open_location_action)

        menu.addSeparator()

        # 删除
        delete_action = QAction("删除", self)
        delete_action.triggered.connect(
            lambda: self.delete_requested.emit(self.segment_info['file_path'])
        )
        menu.addAction(delete_action)

        menu.exec_(position)
