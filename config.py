# -*- coding: utf-8 -*-
"""
配置文件
"""
import os
import json

# 应用配置
APP_NAME = "视频切片提取器"
APP_VERSION = "1.0.0"

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mkv', '.flv', '.ts', '.mov', '.wmv', '.m4v']

# 默认设置
DEFAULT_SETTINGS = {
    'input_folder': '',
    'output_folder': '',
    'current_video_index': 0,
    'window_geometry': None,
    'theme': 'light_cyan.xml'
}

# 设置文件路径
SETTINGS_FILE = os.path.join(os.path.dirname(__file__), 'data', 'settings.json')
PROGRESS_FILE = os.path.join(os.path.dirname(__file__), 'data', 'progress.json')

# 确保data目录存在
os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)


class SettingsManager:
    """设置管理器"""
    
    def __init__(self):
        self.settings = self.load_settings()
        self.progress = self.load_progress()
    
    def load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # 合并默认设置
                    for key, value in DEFAULT_SETTINGS.items():
                        if key not in settings:
                            settings[key] = value
                    return settings
        except Exception as e:
            print(f"加载设置失败: {e}")
        return DEFAULT_SETTINGS.copy()
    
    def save_settings(self):
        """保存设置"""
        try:
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def load_progress(self):
        """加载进度"""
        try:
            if os.path.exists(PROGRESS_FILE):
                with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载进度失败: {e}")
        return {}
    
    def save_progress(self):
        """保存进度"""
        try:
            with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存进度失败: {e}")
    
    def get(self, key, default=None):
        """获取设置值"""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """设置值"""
        self.settings[key] = value
        self.save_settings()
    
    def get_video_progress(self, folder_path):
        """获取文件夹的视频进度"""
        return self.progress.get(folder_path, {})
    
    def set_video_progress(self, folder_path, video_data):
        """设置文件夹的视频进度"""
        self.progress[folder_path] = video_data
        self.save_progress()
    
    def mark_video_extracted(self, folder_path, video_path, segments):
        """标记视频已提取"""
        if folder_path not in self.progress:
            self.progress[folder_path] = {}
        
        if 'extracted_videos' not in self.progress[folder_path]:
            self.progress[folder_path]['extracted_videos'] = {}
        
        self.progress[folder_path]['extracted_videos'][video_path] = {
            'segments': segments,
            'extracted_at': str(os.path.getmtime(video_path))
        }
        self.save_progress()
    
    def is_video_extracted(self, folder_path, video_path):
        """检查视频是否已提取"""
        if folder_path not in self.progress:
            return False
        
        extracted_videos = self.progress[folder_path].get('extracted_videos', {})
        return video_path in extracted_videos


# 全局设置管理器实例
settings_manager = SettingsManager()
