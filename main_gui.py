# -*- coding: utf-8 -*-
"""
视频切片提取软件主界面
"""
from PyQt5.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QApplication, QMainWindow,
    QSplitter, QAction, QFileDialog, QMessageBox, QStatusBar,
    QLabel
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QKeySequence
from qt_material import apply_stylesheet
import sys
import os

from video_player import VideoPlayer
from video_list import VideoListWidget
from config import settings_manager, APP_NAME, APP_VERSION
from utils import check_ffmpeg


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.video_player = None
        self.video_list = None
        self.current_folder = None

        self.init_ui()
        self.setup_connections()
        self.load_settings()
        self.check_dependencies()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(1400, 900)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 上半部分 - 水平分割器
        top_splitter = QSplitter(Qt.Horizontal)

        # 左侧视频列表区域
        self.video_list = VideoListWidget()
        self.video_list.setMinimumWidth(250)
        self.video_list.setMaximumWidth(350)

        # 右侧视频播放器区域
        self.video_player = VideoPlayer()
        self.video_player.setMinimumWidth(800)

        # 添加到上半部分分割器
        top_splitter.addWidget(self.video_list)
        top_splitter.addWidget(self.video_player)

        # 设置分割器比例
        top_splitter.setStretchFactor(0, 1)  # 视频列表
        top_splitter.setStretchFactor(1, 3)  # 视频播放器

        # 下半部分 - 已提取片段列表
        from extracted_segments import ExtractedSegmentsList
        self.extracted_segments = ExtractedSegmentsList()
        self.extracted_segments.setMaximumHeight(200)
        self.extracted_segments.setMinimumHeight(150)

        # 添加到主布局
        main_layout.addWidget(top_splitter, 3)  # 上半部分占3/4
        main_layout.addWidget(self.extracted_segments, 1)  # 下半部分占1/4

        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        self.create_status_bar()

        # 设置焦点策略，使主窗口能接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 打开文件夹
        open_folder_action = QAction('打开视频文件夹(&O)', self)
        open_folder_action.setShortcut(QKeySequence.Open)
        open_folder_action.setStatusTip('选择包含视频文件的文件夹')
        open_folder_action.triggered.connect(self.open_folder)
        file_menu.addAction(open_folder_action)

        file_menu.addSeparator()

        # 设置输出目录
        set_output_action = QAction('设置输出目录(&S)', self)
        set_output_action.setStatusTip('设置提取视频的输出目录')
        set_output_action.triggered.connect(self.set_output_folder)
        file_menu.addAction(set_output_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip('退出应用程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于本软件')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 视频信息标签
        self.video_info_label = QLabel("")
        self.status_bar.addPermanentWidget(self.video_info_label)

        # 输出目录标签
        self.output_folder_label = QLabel("")
        self.status_bar.addPermanentWidget(self.output_folder_label)

        self.update_output_folder_display()

    def setup_connections(self):
        """设置信号连接"""
        # 视频列表信号
        self.video_list.video_selected.connect(self.on_video_selected)
        self.video_list.video_changed.connect(self.on_video_changed)

        # 视频播放器信号
        self.video_player.segment_added.connect(self.on_segment_added)
        self.video_player.video_extracted.connect(self.on_video_extracted)
        self.video_player.previous_video_requested.connect(self.video_list.select_previous_video)
        self.video_player.next_video_requested.connect(self.video_list.select_next_video)

        # 已提取片段列表信号
        self.extracted_segments.segment_selected.connect(self.on_segment_selected)

    def load_settings(self):
        """加载设置"""
        # 恢复窗口几何
        geometry = settings_manager.get('window_geometry')
        if geometry:
            self.restoreGeometry(geometry)

        # 恢复上次打开的文件夹
        last_folder = settings_manager.get('input_folder')
        if last_folder and os.path.exists(last_folder):
            self.load_folder(last_folder)

    def save_settings(self):
        """保存设置"""
        # 保存窗口几何
        settings_manager.set('window_geometry', self.saveGeometry())

    def check_dependencies(self):
        """检查依赖项"""
        if not check_ffmpeg():
            QMessageBox.warning(
                self, "依赖检查",
                "未找到 FFmpeg！\n\n"
                "请确保 FFmpeg 已安装并添加到系统 PATH 中。\n"
                "没有 FFmpeg 将无法进行视频提取。"
            )

    def open_folder(self):
        """打开文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择视频文件夹",
            settings_manager.get('input_folder', '')
        )

        if folder:
            self.load_folder(folder)

    def load_folder(self, folder_path):
        """加载文件夹"""
        self.current_folder = folder_path
        settings_manager.set('input_folder', folder_path)

        # 加载视频列表
        self.video_list.load_folder(folder_path)

        # 加载已提取片段列表
        self.extracted_segments.load_folder(folder_path)

        # 更新状态
        self.status_label.setText(f"已加载文件夹: {os.path.basename(folder_path)}")

        # 更新视频播放器按钮状态
        self.video_player.update_buttons_state()

    def set_output_folder(self):
        """设置输出目录"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择输出目录",
            settings_manager.get('output_folder', '')
        )

        if folder:
            settings_manager.set('output_folder', folder)
            self.update_output_folder_display()

            # 更新视频播放器按钮状态
            self.video_player.update_buttons_state()

            QMessageBox.information(self, "设置成功", f"输出目录已设置为:\n{folder}")

    def update_output_folder_display(self):
        """更新输出目录显示"""
        output_folder = settings_manager.get('output_folder')
        if output_folder:
            self.output_folder_label.setText(f"输出: {os.path.basename(output_folder)}")
        else:
            self.output_folder_label.setText("输出: 未设置")

    def on_video_selected(self, video_path):
        """视频被选中"""
        if self.video_player.load_video(video_path):
            filename = os.path.basename(video_path)
            self.status_label.setText(f"已加载视频: {filename} (点击播放按钮开始播放)")

            # 更新视频信息
            self.video_info_label.setText(f"当前: {filename}")
        else:
            QMessageBox.warning(self, "错误", f"无法加载视频文件:\n{video_path}")

    def on_video_changed(self, current, total):
        """视频切换"""
        self.video_info_label.setText(f"视频 {current}/{total}")

    def on_segment_added(self, start_time, end_time):
        """片段被添加"""
        from utils import format_time
        self.status_label.setText(
            f"已添加片段: {format_time(start_time)} - {format_time(end_time)}"
        )

    def on_video_extracted(self, video_path):
        """视频提取完成"""
        # 更新视频列表中的提取状态
        self.video_list.mark_current_video_extracted()

        # 刷新已提取片段列表
        self.extracted_segments.refresh_segments()

        filename = os.path.basename(video_path)
        self.status_label.setText(f"视频提取完成: {filename}")

    def on_segment_selected(self, segment_path):
        """片段被选中"""
        filename = os.path.basename(segment_path)
        self.status_label.setText(f"选中片段: {filename}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            f"{APP_NAME} v{APP_VERSION}\n\n"
            "一个基于 PyQt5 和 FFmpeg 的视频切片提取工具。\n\n"
            "功能特点:\n"
            "• 支持多种视频格式\n"
            "• 可视化片段选择\n"
            "• 快速无损提取\n"
            "• 进度记忆功能\n\n"
            "快捷键:\n"
            "• 空格键: 播放/暂停\n"
            "• 左右方向键: 快退/快进\n"
            "• 鼠标滚轮: 精确定位"
        )

    def keyPressEvent(self, event):
        """键盘事件处理"""
        # 将键盘事件传递给视频播放器
        if self.video_player:
            self.video_player.keyPressEvent(event)

        if not event.isAccepted():
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """关闭事件"""
        # 保存设置
        self.save_settings()

        # 如果正在提取，询问是否确认退出
        if self.video_player and self.video_player.extractor:
            reply = QMessageBox.question(
                self, "确认退出",
                "正在进行视频提取，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.video_player.cancel_extraction()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    app.setOrganizationName("VideoRawCut")

    # 应用主题
    theme = settings_manager.get('theme', 'light_blue.xml')
    apply_stylesheet(app, theme)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
