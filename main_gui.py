from PyQt5.QtWidgets import (
    QWidget, QPushButton, QHBoxLayout, QVBoxLayout, QLabel,
    QApplication, QMainWindow, QGroupBox, QGridLayout, QMenu,
    QMenuBar, QAction, QFileDialog
)
from PyQt5.QtGui import QIcon
from qt_material import apply_stylesheet
import sys
import os


class MainUi(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频剪辑器")
        self.resize(1300, 900)
        self.central_widget = QWidget()
        self.main_layout = QVBoxLayout(self.central_widget)
        self.setCentralWidget(self.central_widget)

        video_widget = VideoWidget()
        control_widget = ControlWidget()

        self.main_layout.addWidget(video_widget, 6)
        self.main_layout.addWidget(control_widget, 1)

        self.menu_bar = self.menuBar()

        # 定义变量
        self.video_list = []
        self.current_path = None

        self._init_menu()

    def _init_menu(self):
        file_menu = self.menu_bar.addMenu('文件(&F)')
        open_folder_action = QAction(QIcon('./res/Folder.png'), '打开文件夹(&O)', self)
        open_folder_action.triggered.connect(self._select_folder)
        file_menu.addAction(open_folder_action)

    def _select_folder(self):
        current_path = QFileDialog.getExistingDirectory(
            self,
            "选择工作目录",
            "./",  # 默认起始路径
        )
        if not current_path:
            return 0
        self.current_path = current_path
        self.video_list = []
        for root, dirs, file in os.walk(current_path):
            for f in file:
                filepath = os.path.join(root, f)
                _, ext = os.path.splitext(f)
                if ext.lower() not in (
                        ".mp4", ".avi", ".mkv", ".flv", ".ts", ".mov"
                ):
                    continue
                self.video_list.append(filepath)
        print(self.video_list)


class VideoWidget(QGroupBox):
    def __init__(self):
        super().__init__()
        self.main_layout = QHBoxLayout(self)


class ControlWidget(QGroupBox):
    def __init__(self):
        super().__init__()
        self.main_layout = QGridLayout(self)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    apply_stylesheet(app, "light_cyan.xml")
    win = MainUi()
    win.show()
    sys.exit(app.exec_())
