# -*- coding: utf-8 -*-
"""
视频播放器组件
"""
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSlider, QLabel,
    QFrame, QSizePolicy, QSpacerItem, QProgressBar, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QUrl
from PyQt5.QtGui import QFont
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from utils import format_time, VideoExtractor
from config import settings_manager


class VideoPlayer(QWidget):
    """视频播放器组件"""

    # 信号定义
    position_changed = pyqtSignal(int)  # 播放位置改变
    duration_changed = pyqtSignal(int)  # 视频时长改变
    segment_added = pyqtSignal(float, float)  # 添加片段
    video_extracted = pyqtSignal(str)  # 视频提取完成
    previous_video_requested = pyqtSignal()  # 请求上一个视频
    next_video_requested = pyqtSignal()  # 请求下一个视频

    def __init__(self):
        super().__init__()
        self.media_player = QMediaPlayer()
        self.video_widget = QVideoWidget()
        self.current_video_path = None
        self.video_duration = 0
        self.segments = []  # 存储选择的片段 [(start, end), ...]
        self.segment_start = None
        self.is_selecting_segment = False

        # 提取相关
        self.extractor = None

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 视频显示区域
        self.video_widget.setMinimumHeight(400)
        self.video_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(self.video_widget)

        # 控制面板
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_layout = QVBoxLayout(control_frame)

        # 进度条
        progress_layout = QHBoxLayout()
        self.time_label = QLabel("00:00")
        self.time_label.setMinimumWidth(50)

        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setMinimum(0)
        self.position_slider.setMaximum(1000)

        self.duration_label = QLabel("00:00")
        self.duration_label.setMinimumWidth(50)

        progress_layout.addWidget(self.time_label)
        progress_layout.addWidget(self.position_slider)
        progress_layout.addWidget(self.duration_label)

        # 第一行控制按钮
        button_layout1 = QHBoxLayout()

        self.play_button = QPushButton("播放")
        self.play_button.setMinimumWidth(80)
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106EBE;
            }
            QPushButton:pressed {
                background-color: #005A9E;
            }
        """)

        # 倍速控制
        self.speed_button = QPushButton("1.0x")
        self.speed_button.setMinimumWidth(60)
        self.speed_button.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        self.current_speed = 1.0
        self.speed_options = [1.0, 1.25, 1.5, 2.0, 3.0, 5.0]

        # 上一个/下一个视频按钮
        self.prev_video_button = QPushButton("上一个")
        self.prev_video_button.setMinimumWidth(80)
        self.prev_video_button.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)

        self.next_video_button = QPushButton("下一个")
        self.next_video_button.setMinimumWidth(80)
        self.next_video_button.setStyleSheet(self.prev_video_button.styleSheet())

        button_layout1.addWidget(self.play_button)
        button_layout1.addWidget(self.speed_button)
        button_layout1.addItem(QSpacerItem(20, 0, QSizePolicy.Expanding))
        button_layout1.addWidget(self.prev_video_button)
        button_layout1.addWidget(self.next_video_button)

        # 第二行控制按钮
        button_layout2 = QHBoxLayout()

        self.segment_start_button = QPushButton("设置起点")
        self.segment_start_button.setMinimumWidth(80)
        self.segment_start_button.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: black;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E0A800;
            }
            QPushButton:disabled {
                background-color: #6C757D;
                color: white;
            }
        """)

        self.segment_end_button = QPushButton("设置终点")
        self.segment_end_button.setMinimumWidth(80)
        self.segment_end_button.setEnabled(False)
        self.segment_end_button.setStyleSheet(self.segment_start_button.styleSheet())

        self.add_segment_button = QPushButton("添加片段")
        self.add_segment_button.setMinimumWidth(80)
        self.add_segment_button.setEnabled(False)
        self.add_segment_button.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)

        self.extract_button = QPushButton("开始提取")
        self.extract_button.setMinimumWidth(80)
        self.extract_button.setEnabled(False)
        self.extract_button.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)

        button_layout2.addWidget(self.segment_start_button)
        button_layout2.addWidget(self.segment_end_button)
        button_layout2.addWidget(self.add_segment_button)
        button_layout2.addItem(QSpacerItem(20, 0, QSizePolicy.Expanding))
        button_layout2.addWidget(self.extract_button)

        # 片段信息显示
        self.segment_info_label = QLabel("当前片段: 无")
        self.segment_info_label.setFont(QFont("Arial", 9))

        # 提取进度条
        self.extract_progress = QProgressBar()
        self.extract_progress.setVisible(False)

        control_layout.addLayout(progress_layout)
        control_layout.addLayout(button_layout1)
        control_layout.addLayout(button_layout2)
        control_layout.addWidget(self.segment_info_label)
        control_layout.addWidget(self.extract_progress)

        layout.addWidget(control_frame)

        # 设置媒体播放器输出
        self.media_player.setVideoOutput(self.video_widget)

    def setup_connections(self):
        """设置信号连接"""
        # 媒体播放器信号
        self.media_player.positionChanged.connect(self.on_position_changed)
        self.media_player.durationChanged.connect(self.on_duration_changed)
        self.media_player.stateChanged.connect(self.on_state_changed)
        self.media_player.error.connect(self.on_media_error)

        # 控件信号
        self.play_button.clicked.connect(self.toggle_play)
        self.speed_button.clicked.connect(self.cycle_speed)
        self.prev_video_button.clicked.connect(self.previous_video)
        self.next_video_button.clicked.connect(self.next_video)
        self.position_slider.sliderPressed.connect(self.on_slider_pressed)
        self.position_slider.sliderReleased.connect(self.on_slider_released)
        self.position_slider.valueChanged.connect(self.on_slider_value_changed)
        self.position_slider.mousePressEvent = self.on_slider_click

        # 片段控制信号
        self.segment_start_button.clicked.connect(self.set_segment_start)
        self.segment_end_button.clicked.connect(self.set_segment_end)
        self.add_segment_button.clicked.connect(self.add_current_segment)
        self.extract_button.clicked.connect(self.start_extraction)

    def load_video(self, video_path):
        """加载视频"""
        try:
            if not os.path.exists(video_path):
                print(f"视频文件不存在: {video_path}")
                return False

            # 检查文件大小
            file_size = os.path.getsize(video_path)
            if file_size == 0:
                print(f"视频文件为空: {video_path}")
                return False

            self.current_video_path = video_path
            self.media_player.setMedia(QMediaContent(QUrl.fromLocalFile(video_path)))

            # 重置片段选择
            self.segments = []
            self.segment_start = None
            self.is_selecting_segment = False
            self.update_segment_display()
            self.update_buttons_state()

            # 重置播放速度
            self.current_speed = 1.0
            self.speed_button.setText("1.0x")
            self.media_player.setPlaybackRate(1.0)

            return True
        except Exception as e:
            print(f"加载视频失败: {e}")
            return False

    def toggle_play(self):
        """切换播放/暂停"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
        else:
            self.media_player.play()

    def cycle_speed(self):
        """循环切换播放速度"""
        current_index = self.speed_options.index(self.current_speed)
        next_index = (current_index + 1) % len(self.speed_options)
        self.current_speed = self.speed_options[next_index]

        # 设置播放速度
        self.media_player.setPlaybackRate(self.current_speed)

        # 更新按钮文本
        self.speed_button.setText(f"{self.current_speed}x")

    def previous_video(self):
        """上一个视频"""
        self.previous_video_requested.emit()

    def next_video(self):
        """下一个视频"""
        self.next_video_requested.emit()

    def on_slider_click(self, event):
        """进度条点击事件"""
        if event.button() == Qt.LeftButton:
            # 计算点击位置对应的时间
            slider_width = self.position_slider.width()
            click_pos = event.x()
            ratio = click_pos / slider_width

            # 设置播放位置
            new_position = int(ratio * self.video_duration)
            self.media_player.setPosition(new_position)

        # 调用原始的鼠标按下事件
        QSlider.mousePressEvent(self.position_slider, event)

    def on_position_changed(self, position):
        """播放位置改变"""
        if not self.position_slider.isSliderDown():
            self.position_slider.setValue(int(position / self.video_duration * 1000) if self.video_duration > 0 else 0)

        self.time_label.setText(format_time(position / 1000))
        self.position_changed.emit(position)

        # 更新当前片段信息
        if self.is_selecting_segment and self.segment_start is not None:
            current_time = position / 1000
            duration = current_time - self.segment_start
            self.segment_info_label.setText(
                f"当前片段: {format_time(self.segment_start)} - {format_time(current_time)} "
                f"(时长: {format_time(duration)})"
            )

    def on_duration_changed(self, duration):
        """视频时长改变"""
        self.video_duration = duration
        self.duration_label.setText(format_time(duration / 1000))
        self.duration_changed.emit(duration)

    def on_state_changed(self, state):
        """播放状态改变"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setText("暂停")
        else:
            self.play_button.setText("播放")

    def on_media_error(self, error):
        """媒体播放错误"""
        error_messages = {
            QMediaPlayer.NoError: "无错误",
            QMediaPlayer.ResourceError: "资源错误：无法解析媒体资源",
            QMediaPlayer.FormatError: "格式错误：不支持的媒体格式",
            QMediaPlayer.NetworkError: "网络错误：网络连接问题",
            QMediaPlayer.AccessDeniedError: "访问被拒绝：权限不足",
            QMediaPlayer.ServiceMissingError: "服务缺失：缺少媒体服务"
        }

        error_msg = error_messages.get(error, f"未知错误: {error}")
        print(f"媒体播放错误: {error_msg}")

        if error != QMediaPlayer.NoError:
            QMessageBox.warning(
                self, "播放错误",
                f"视频播放出现问题:\n{error_msg}\n\n"
                "请检查:\n"
                "1. 视频文件是否损坏\n"
                "2. 视频格式是否支持\n"
                "3. 系统是否安装了必要的解码器"
            )

    def on_slider_pressed(self):
        """进度条按下"""
        pass

    def on_slider_released(self):
        """进度条释放"""
        position = int(self.position_slider.value() / 1000 * self.video_duration)
        self.media_player.setPosition(position)

    def on_slider_value_changed(self, value):
        """进度条值改变"""
        if self.position_slider.isSliderDown():
            position = int(value / 1000 * self.video_duration)
            self.time_label.setText(format_time(position / 1000))

    def set_segment_start(self):
        """设置片段起点"""
        current_position = self.media_player.position() / 1000
        self.segment_start = current_position
        self.is_selecting_segment = True
        self.segment_end_button.setEnabled(True)
        self.segment_info_label.setText(f"片段起点: {format_time(current_position)}")

    def set_segment_end(self):
        """设置片段终点"""
        if self.segment_start is None:
            return

        current_position = self.media_player.position() / 1000
        if current_position <= self.segment_start:
            QMessageBox.warning(self, "警告", "终点时间必须大于起点时间！")
            return

        duration = current_position - self.segment_start
        self.segment_info_label.setText(
            f"当前片段: {format_time(self.segment_start)} - {format_time(current_position)} "
            f"(时长: {format_time(duration)})"
        )
        self.add_segment_button.setEnabled(True)

    def add_current_segment(self):
        """添加当前片段"""
        if self.segment_start is None:
            return

        current_position = self.media_player.position() / 1000
        if current_position <= self.segment_start:
            QMessageBox.warning(self, "警告", "终点时间必须大于起点时间！")
            return

        # 添加片段
        self.segments.append((self.segment_start, current_position))
        self.segment_added.emit(self.segment_start, current_position)

        # 重置状态
        self.segment_start = None
        self.is_selecting_segment = False
        self.update_segment_display()
        self.update_buttons_state()



    def update_segment_display(self):
        """更新片段显示"""
        if not self.is_selecting_segment:
            self.segment_info_label.setText("当前片段: 无")

    def update_buttons_state(self):
        """更新按钮状态"""
        has_video = self.current_video_path is not None
        has_segments = len(self.segments) > 0
        has_output_folder = bool(settings_manager.get('output_folder'))

        self.segment_start_button.setEnabled(has_video)
        self.segment_end_button.setEnabled(self.is_selecting_segment)
        self.add_segment_button.setEnabled(self.is_selecting_segment and self.segment_start is not None)
        self.extract_button.setEnabled(has_segments and has_output_folder)

    def start_extraction(self):
        """开始提取视频"""
        if not self.segments or not self.current_video_path:
            return

        output_folder = settings_manager.get('output_folder')
        if not output_folder:
            QMessageBox.warning(self, "警告", "请先设置输出目录！")
            return

        # 创建提取线程
        self.extractor = VideoExtractor(self.current_video_path, output_folder, self.segments)
        self.extractor.progress_updated.connect(self.on_extraction_progress)
        self.extractor.extraction_finished.connect(self.on_extraction_finished)

        # 显示进度条
        self.extract_progress.setVisible(True)
        self.extract_progress.setValue(0)
        self.extract_button.setText("取消提取")
        self.extract_button.clicked.disconnect()
        self.extract_button.clicked.connect(self.cancel_extraction)

        # 开始提取
        self.extractor.start()

    def cancel_extraction(self):
        """取消提取"""
        if self.extractor:
            self.extractor.cancel()

    def on_extraction_progress(self, progress):
        """提取进度更新"""
        self.extract_progress.setValue(progress)

    def on_extraction_finished(self, success, message):
        """提取完成"""
        self.extract_progress.setVisible(False)
        self.extract_button.setText("开始提取")
        self.extract_button.clicked.disconnect()
        self.extract_button.clicked.connect(self.start_extraction)

        if success:
            QMessageBox.information(self, "成功", message)
            # 标记视频已提取
            folder_path = settings_manager.get('input_folder')
            if folder_path:
                settings_manager.mark_video_extracted(folder_path, self.current_video_path, self.segments)
                # 发送视频提取完成信号
                self.video_extracted.emit(self.current_video_path)
        else:
            QMessageBox.critical(self, "错误", message)

        self.extractor = None

    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_Space:
            self.toggle_play()
            event.accept()
        elif event.key() == Qt.Key_Left:
            # 快退5秒
            position = max(0, self.media_player.position() - 5000)
            self.media_player.setPosition(position)
            event.accept()
        elif event.key() == Qt.Key_Right:
            # 快进5秒
            position = min(self.video_duration, self.media_player.position() + 5000)
            self.media_player.setPosition(position)
            event.accept()
        else:
            super().keyPressEvent(event)

    def wheelEvent(self, event):
        """鼠标滚轮事件"""
        if self.video_widget.underMouse():
            # 滚轮控制视频进度
            delta = event.angleDelta().y()
            if delta > 0:
                # 向前滚动，快进
                position = min(self.video_duration, self.media_player.position() + 2000)
            else:
                # 向后滚动，快退
                position = max(0, self.media_player.position() - 2000)

            self.media_player.setPosition(position)
            event.accept()
        else:
            super().wheelEvent(event)
