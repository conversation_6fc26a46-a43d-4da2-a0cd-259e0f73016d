#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频切片提取器快速启动脚本（禁用缩略图）
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置环境变量禁用缩略图
os.environ['DISABLE_THUMBNAILS'] = '1'

try:
    from main_gui import main
    main()
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖:")
    print("pip install PyQt5 qt-material")
    sys.exit(1)
except Exception as e:
    print(f"程序运行错误: {e}")
    sys.exit(1)
