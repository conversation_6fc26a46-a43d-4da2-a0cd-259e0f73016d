# -*- coding: utf-8 -*-
"""
工具函数
"""
import os
import subprocess
from PyQt5.QtCore import QThread, pyqtSignal
from config import SUPPORTED_VIDEO_FORMATS


def get_video_files(folder_path):
    """获取文件夹中的所有视频文件"""
    video_files = []
    if not os.path.exists(folder_path):
        return video_files

    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            _, ext = os.path.splitext(file)
            if ext.lower() in SUPPORTED_VIDEO_FORMATS:
                video_files.append(file_path)

    return sorted(video_files)


def format_time(seconds):
    """格式化时间显示"""
    if seconds < 0:
        return "00:00"

    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)

    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"


def get_video_duration(video_path):
    """获取视频时长"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            duration = float(data['format']['duration'])
            return duration
    except Exception as e:
        print(f"获取视频时长失败: {e}")
    return 0


def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)

            # 获取视频流信息
            video_stream = None
            for stream in data['streams']:
                if stream['codec_type'] == 'video':
                    video_stream = stream
                    break

            if video_stream:
                return {
                    'duration': float(data['format']['duration']),
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                    'codec': video_stream.get('codec_name', ''),
                    'bitrate': int(data['format'].get('bit_rate', 0))
                }
    except Exception as e:
        print(f"获取视频信息失败: {e}")
    return None


class VideoExtractor(QThread):
    """视频提取线程"""
    progress_updated = pyqtSignal(int)  # 进度更新信号
    extraction_finished = pyqtSignal(bool, str)  # 提取完成信号

    def __init__(self, video_path, output_folder, segments):
        super().__init__()
        self.video_path = video_path
        self.output_folder = output_folder
        self.segments = segments
        self.is_cancelled = False

    def cancel(self):
        """取消提取"""
        self.is_cancelled = True

    def run(self):
        """执行视频提取"""
        try:
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder)

            video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            total_segments = len(self.segments)

            for i, segment in enumerate(self.segments):
                if self.is_cancelled:
                    break

                # 解析片段信息
                if len(segment) >= 3:
                    start_time, end_time, tag = segment
                else:
                    start_time, end_time = segment[:2]
                    tag = '无标签'

                # 生成输出文件名（包含标签）
                safe_tag = "".join(c for c in tag if c.isalnum() or c in (' ', '-', '_')).rstrip()
                if safe_tag and safe_tag != '无标签':
                    output_filename = f"{video_name}_{safe_tag}_segment_{i+1:02d}.mp4"
                else:
                    output_filename = f"{video_name}_segment_{i+1:02d}.mp4"

                output_path = os.path.join(self.output_folder, output_filename)

                # 使用ffmpeg提取视频片段
                cmd = [
                    'ffmpeg', '-i', self.video_path,
                    '-ss', str(start_time),
                    '-to', str(end_time),
                    '-c', 'copy',  # 使用复制模式，速度更快
                    '-avoid_negative_ts', 'make_zero',
                    '-y',  # 覆盖输出文件
                    output_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    self.extraction_finished.emit(False, f"提取失败: {result.stderr}")
                    return

                # 更新进度
                progress = int((i + 1) / total_segments * 100)
                self.progress_updated.emit(progress)

            if not self.is_cancelled:
                self.extraction_finished.emit(True, f"成功提取 {total_segments} 个片段")

        except Exception as e:
            self.extraction_finished.emit(False, f"提取过程中出错: {str(e)}")


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def generate_thumbnail(video_path, time_pos, output_path, width=160, height=90):
    """生成视频缩略图"""
    try:
        cmd = [
            'ffmpeg', '-i', video_path,
            '-ss', str(time_pos),
            '-vframes', '1',
            '-vf', f'scale={width}:{height}',
            '-y', output_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"生成缩略图失败: {e}")
        return False


def get_or_create_thumbnail(video_path, width=80, height=45):
    """获取或创建视频缩略图"""
    try:
        # 创建缩略图缓存目录
        cache_dir = os.path.join(os.path.dirname(__file__), 'data', 'thumbnails')
        os.makedirs(cache_dir, exist_ok=True)

        # 生成缩略图文件名
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        thumbnail_path = os.path.join(cache_dir, f"{video_name}_{width}x{height}.jpg")

        # 如果缩略图不存在，生成它
        if not os.path.exists(thumbnail_path):
            # 获取视频时长的10%位置作为缩略图时间点
            duration = get_video_duration(video_path)
            time_pos = max(1, duration * 0.1) if duration > 0 else 1

            if generate_thumbnail(video_path, time_pos, thumbnail_path, width, height):
                return thumbnail_path
            else:
                return None
        else:
            return thumbnail_path
    except Exception as e:
        print(f"获取缩略图失败: {e}")
        return None
