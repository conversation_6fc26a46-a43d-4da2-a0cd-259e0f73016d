# -*- coding: utf-8 -*-
"""
标签管理器
"""
import os
import json
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QInputDialog,
    QDialogButtonBox, QComboBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from config import settings_manager


class TagManager:
    """标签管理器"""
    
    def __init__(self):
        self.tags_file = os.path.join(os.path.dirname(__file__), 'data', 'tags.json')
        self.tags = self.load_tags()
    
    def load_tags(self):
        """加载标签"""
        try:
            if os.path.exists(self.tags_file):
                with open(self.tags_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        return json.loads(content)
        except Exception as e:
            print(f"加载标签失败: {e}")
        
        # 返回默认标签
        return [
            {"name": "精彩片段", "order": 1},
            {"name": "重要内容", "order": 2},
            {"name": "搞笑片段", "order": 3},
            {"name": "教程片段", "order": 4},
            {"name": "其他", "order": 5}
        ]
    
    def save_tags(self):
        """保存标签"""
        try:
            os.makedirs(os.path.dirname(self.tags_file), exist_ok=True)
            with open(self.tags_file, 'w', encoding='utf-8') as f:
                json.dump(self.tags, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存标签失败: {e}")
    
    def get_tags_sorted(self):
        """获取按顺序排列的标签"""
        return sorted(self.tags, key=lambda x: x.get('order', 999))
    
    def add_tag(self, name, order=None):
        """添加标签"""
        if order is None:
            order = max([tag.get('order', 0) for tag in self.tags], default=0) + 1
        
        # 检查是否已存在
        for tag in self.tags:
            if tag['name'] == name:
                return False
        
        self.tags.append({"name": name, "order": order})
        self.save_tags()
        return True
    
    def remove_tag(self, name):
        """删除标签"""
        self.tags = [tag for tag in self.tags if tag['name'] != name]
        self.save_tags()
    
    def update_tag(self, old_name, new_name):
        """更新标签名称"""
        for tag in self.tags:
            if tag['name'] == old_name:
                tag['name'] = new_name
                self.save_tags()
                return True
        return False
    
    def reorder_tags(self, tag_orders):
        """重新排序标签"""
        for tag_name, order in tag_orders.items():
            for tag in self.tags:
                if tag['name'] == tag_name:
                    tag['order'] = order
        self.save_tags()


class TagManagementDialog(QDialog):
    """标签管理对话框"""
    
    tags_changed = pyqtSignal()  # 标签改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tag_manager = TagManager()
        self.init_ui()
        self.load_tags()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("标签管理")
        self.setMinimumSize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("管理视频片段标签")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 标签列表
        self.tag_list = QListWidget()
        self.tag_list.setMinimumHeight(300)
        layout.addWidget(self.tag_list)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("添加标签")
        self.add_button.clicked.connect(self.add_tag)
        
        self.edit_button = QPushButton("编辑标签")
        self.edit_button.clicked.connect(self.edit_tag)
        
        self.delete_button = QPushButton("删除标签")
        self.delete_button.clicked.connect(self.delete_tag)
        
        self.move_up_button = QPushButton("上移")
        self.move_up_button.clicked.connect(self.move_up)
        
        self.move_down_button = QPushButton("下移")
        self.move_down_button.clicked.connect(self.move_down)
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.move_up_button)
        button_layout.addWidget(self.move_down_button)
        
        layout.addLayout(button_layout)
        
        # 对话框按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_tags(self):
        """加载标签到列表"""
        self.tag_list.clear()
        for tag in self.tag_manager.get_tags_sorted():
            item = QListWidgetItem(tag['name'])
            item.setData(Qt.UserRole, tag)
            self.tag_list.addItem(item)
    
    def add_tag(self):
        """添加标签"""
        name, ok = QInputDialog.getText(self, "添加标签", "标签名称:")
        if ok and name.strip():
            if self.tag_manager.add_tag(name.strip()):
                self.load_tags()
                self.tags_changed.emit()
            else:
                QMessageBox.warning(self, "警告", "标签已存在！")
    
    def edit_tag(self):
        """编辑标签"""
        current_item = self.tag_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要编辑的标签！")
            return
        
        old_name = current_item.text()
        new_name, ok = QInputDialog.getText(self, "编辑标签", "标签名称:", text=old_name)
        if ok and new_name.strip() and new_name.strip() != old_name:
            if self.tag_manager.update_tag(old_name, new_name.strip()):
                self.load_tags()
                self.tags_changed.emit()
            else:
                QMessageBox.warning(self, "警告", "更新标签失败！")
    
    def delete_tag(self):
        """删除标签"""
        current_item = self.tag_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要删除的标签！")
            return
        
        tag_name = current_item.text()
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除标签 '{tag_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.tag_manager.remove_tag(tag_name)
            self.load_tags()
            self.tags_changed.emit()
    
    def move_up(self):
        """上移标签"""
        current_row = self.tag_list.currentRow()
        if current_row > 0:
            self.swap_tags(current_row, current_row - 1)
            self.tag_list.setCurrentRow(current_row - 1)
    
    def move_down(self):
        """下移标签"""
        current_row = self.tag_list.currentRow()
        if current_row < self.tag_list.count() - 1:
            self.swap_tags(current_row, current_row + 1)
            self.tag_list.setCurrentRow(current_row + 1)
    
    def swap_tags(self, row1, row2):
        """交换两个标签的顺序"""
        item1 = self.tag_list.item(row1)
        item2 = self.tag_list.item(row2)
        
        tag1 = item1.data(Qt.UserRole)
        tag2 = item2.data(Qt.UserRole)
        
        # 交换order值
        tag1['order'], tag2['order'] = tag2['order'], tag1['order']
        
        self.tag_manager.save_tags()
        self.load_tags()
        self.tags_changed.emit()


class AddSegmentDialog(QDialog):
    """添加片段对话框"""
    
    def __init__(self, start_time, end_time, parent=None):
        super().__init__(parent)
        self.start_time = start_time
        self.end_time = end_time
        self.tag_manager = TagManager()
        self.selected_tag = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("添加视频片段")
        self.setMinimumSize(350, 200)
        
        layout = QVBoxLayout(self)
        
        # 片段信息
        from utils import format_time
        info_label = QLabel(f"片段时间: {format_time(self.start_time)} → {format_time(self.end_time)}")
        info_label.setFont(QFont("Arial", 12))
        layout.addWidget(info_label)
        
        duration = self.end_time - self.start_time
        duration_label = QLabel(f"片段时长: {format_time(duration)}")
        duration_label.setFont(QFont("Arial", 10))
        layout.addWidget(duration_label)
        
        # 标签选择
        tag_layout = QHBoxLayout()
        tag_layout.addWidget(QLabel("选择标签:"))
        
        self.tag_combo = QComboBox()
        self.tag_combo.setMinimumWidth(200)
        
        # 加载标签
        tags = self.tag_manager.get_tags_sorted()
        for tag in tags:
            self.tag_combo.addItem(tag['name'])
        
        # 默认选择第一个标签
        if tags:
            self.selected_tag = tags[0]['name']
            self.tag_combo.setCurrentIndex(0)
        
        self.tag_combo.currentTextChanged.connect(self.on_tag_changed)
        
        tag_layout.addWidget(self.tag_combo)
        layout.addLayout(tag_layout)
        
        # 对话框按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def on_tag_changed(self, tag_name):
        """标签改变"""
        self.selected_tag = tag_name
    
    def get_selected_tag(self):
        """获取选择的标签"""
        return self.selected_tag


# 全局标签管理器实例
tag_manager = TagManager()
